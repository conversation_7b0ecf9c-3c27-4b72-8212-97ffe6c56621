#!/usr/bin/env python3
"""
CSV文件合并脚本
合并 top2k_enhanced_null_v14_copy.csv 和 top2k_enhanced_food_v13_bingyuhuo.csv
优先使用v13 food版本的数据，如果bizId不存在则使用v14 null版本
"""

import pandas as pd
import os
from pathlib import Path

def merge_csv_files():
    """合并两个CSV文件"""
    
    # 文件路径
    v14_file = "tests/top2k_enhanced_null_v14_copy.csv"
    v13_file = "tests/top2k_enhanced_food_v13_bingyuhuo.csv"
    output_file = "tests/top2k_enhanced_food_v13_bingyuhuo_v14_merged.csv"
    
    # 检查文件是否存在
    if not os.path.exists(v14_file):
        print(f"错误：文件 {v14_file} 不存在")
        return
    
    if not os.path.exists(v13_file):
        print(f"错误：文件 {v13_file} 不存在")
        return
    
    print("开始读取CSV文件...")
    
    # 读取CSV文件
    try:
        df_v14 = pd.read_csv(v14_file)
        df_v13 = pd.read_csv(v13_file)
        
        print(f"v14文件读取成功，共 {len(df_v14)} 行")
        print(f"v13文件读取成功，共 {len(df_v13)} 行")
        
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return
    
    # 显示列信息
    print(f"\nv14文件列：{list(df_v14.columns)}")
    print(f"v13文件列：{list(df_v13.columns)}")
    
    # 确保v13文件有version列，如果有则删除它以匹配v14的列结构
    if 'version' in df_v13.columns:
        df_v13_cleaned = df_v13.drop('version', axis=1)
        print("已从v13数据中移除version列")
    else:
        df_v13_cleaned = df_v13.copy()
    
    # 检查列是否匹配
    if list(df_v14.columns) != list(df_v13_cleaned.columns):
        print("警告：两个文件的列结构不完全匹配")
        print(f"v14列：{list(df_v14.columns)}")
        print(f"v13列（清理后）：{list(df_v13_cleaned.columns)}")
    
    # 获取v13中的bizId集合
    v13_bizids = set(df_v13_cleaned['bizId'])
    print(f"\nv13文件中有 {len(v13_bizids)} 个唯一的bizId")
    
    # 从v14中筛选出不在v13中的数据
    df_v14_filtered = df_v14[~df_v14['bizId'].isin(v13_bizids)]
    print(f"v14文件中不在v13中的数据有 {len(df_v14_filtered)} 行")
    
    # 合并数据：v13的数据 + v14中不在v13中的数据
    merged_df = pd.concat([df_v13_cleaned, df_v14_filtered], ignore_index=True)
    
    print(f"\n合并后总共 {len(merged_df)} 行数据")
    
    # 检查是否有重复的bizId
    duplicate_bizids = merged_df[merged_df.duplicated('bizId', keep=False)]
    if len(duplicate_bizids) > 0:
        print(f"警告：发现 {len(duplicate_bizids)} 行重复的bizId")
        print("重复的bizId：")
        print(duplicate_bizids['bizId'].unique())
    else:
        print("✓ 没有发现重复的bizId")
    
    # 保存合并后的文件
    try:
        merged_df.to_csv(output_file, index=False)
        print(f"\n✓ 合并完成！文件已保存到：{output_file}")
        
        # 显示统计信息
        print("\n=== 合并统计 ===")
        print(f"v13 food版本数据：{len(df_v13_cleaned)} 行")
        print(f"v14 null版本数据（不在v13中）：{len(df_v14_filtered)} 行")
        print(f"合并后总数据：{len(merged_df)} 行")
        
        # 显示enhance_classes分布
        print(f"\n=== enhance_classes分布 ===")
        enhance_classes_dist = merged_df['enhance_classes'].value_counts()
        for class_name, count in enhance_classes_dist.items():
            print(f"{class_name}: {count} 行")
            
    except Exception as e:
        print(f"保存文件时出错：{e}")
        return

def main():
    """主函数"""
    print("CSV文件合并工具")
    print("=" * 50)
    
    merge_csv_files()
    
    print("\n处理完成！")

if __name__ == "__main__":
    main()
