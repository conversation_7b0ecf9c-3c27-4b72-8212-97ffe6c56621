#!/usr/bin/env python3
# -*- encoding: utf-8 -*-
"""
对tests/top2000.csv进行多版本画质优化测试的脚本
支持不同的enhance_classes和version配置
使用GenericAPICaller调用服务，支持并发处理和进度条显示
"""

import json
import time
import random
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import pandas as pd
from pathlib import Path
from PIL import Image

# 导入现有的API服务模块
from api_services import GenericAPICaller
from io_utils import load_image

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CoverEnhanceMultiVersionTest")

# 配置参数
DOWNLOAD_IMAGES = True  # 是否下载和拼接图片


def resize_image_to_match(img1, img2):
    """将img1的尺寸调整为与img2相同"""
    if img1 is None or img2 is None:
        return img1

    target_size = img2.size
    return img1.resize(target_size, Image.Resampling.LANCZOS)


def create_combined_image(original_img, enhanced_img, content_id, cover_source, output_dir):
    """创建拼接图片：原图 | 增强后图片"""
    try:
        # 确保所有图片都存在
        if original_img is None or enhanced_img is None:
            logger.warning(f"缺少必要图片，跳过拼接: {content_id}")
            return False

        # 调整原图尺寸与增强图一致
        original_img_resized = resize_image_to_match(original_img, enhanced_img)

        # 创建拼接图片
        images = [original_img_resized, enhanced_img]
        width = sum(img.width for img in images)
        height = max(img.height for img in images)

        combined = Image.new('RGB', (width, height), (255, 255, 255))

        x_offset = 0
        for img in images:
            combined.paste(img, (x_offset, 0))
            x_offset += img.width

        # 保存图片
        output_filename = f"{content_id}_{cover_source}.jpg"
        output_path = output_dir / output_filename
        combined.save(output_path)
        logger.info(f"拼接图片保存成功: {output_path}")
        return True

    except Exception as e:
        logger.error(f"拼接图片失败 {content_id}: {e}")
        return False


def smart_cover_enhancement(data):
    """
    调用智能封面增强服务（使用通用调用器）
    """
    caller = GenericAPICaller(
        scene_name="smart_cover_enhancement",
        post_url="https://paiplusinference.alipay.com/inference/7a2f7401cee56342_smart_cover_enhancement/v1",
        input_key="data",
        output_key="output",
        output_json_parse=True,  # 输出需要解析为JSON
    )
    timeout = data.get("timeout", 30000)
    return caller.call(data, timeout=timeout)


def process_single_row(row_data, test_config, output_dir=None):
    """处理单行数据"""
    content_id = row_data['content_id']
    title = row_data.get('title', '')
    cat = row_data.get('cate_l1', '')

    # 处理封面ID的逻辑：优先使用optimize_aggregation_new_cover，如果为空则使用cover_static
    optimize_cover = row_data.get('optimize_aggregation_new_cover', '')
    static_cover = row_data.get('cover_static', '')

    # 判断使用哪个封面ID
    if pd.notna(optimize_cover) and str(optimize_cover).strip():
        smart_cover_id = optimize_cover
        cover_source = "optimized"
    elif pd.notna(static_cover) and str(static_cover).strip():
        smart_cover_id = static_cover
        cover_source = "original"
    else:
        # 两个字段都为空，跳过处理
        return None, f"Content {content_id}: Both optimize_aggregation_new_cover and cover_static are empty, skipping"
    
    # 改成使用原始封面
    smart_cover_id = static_cover
    cover_source = "original"

    try:
        # 构造输入数据，使用测试配置
        input_data = {
            "cover_id": smart_cover_id,
            "cover_url": "",
            "title": title,
            "cat": cat,
            "video_name": "",
            "video_id": "",
            "contentId": content_id,
            'enhance_classes': test_config['enhance_classes'],
            "version2lutname": test_config['version2lutname'],
            "logs": {},
            "version": test_config['version'],
            "mode": "maya"  # 使用maya模式
        }

        # 调用增强服务
        result = smart_cover_enhancement(input_data)

        # 检查增强是否成功
        enhanced_cover_id = None
        if isinstance(result, dict) and 'new_cover_id' in result and result['new_cover_id']:
            enhanced_cover_id = result['new_cover_id']

        # 只有增强成功才构造输出行
        if enhanced_cover_id:
            # 构造输出行
            output_row = {
                'bizId': content_id,
                'aftsId': enhanced_cover_id,
                'subVersion': test_config['version'],  # 使用输入的version
                'bizType': 'home_page_aigc',
                'type': 'cover',
                'dt': '20250821',
                'coverSource': cover_source,  # 添加封面来源说明
                'enhance_classes': ','.join(test_config['enhance_classes']),  # 添加增强类别
                'lut_name': test_config['version2lutname'].get(test_config['version'], 'unknown')  # 添加LUT名称
            }

            # 如果启用图片下载，下载并拼接图片
            if DOWNLOAD_IMAGES and output_dir:
                try:
                    # 下载原图
                    original_img = load_image(smart_cover_id)
                    # 下载增强后的图片
                    enhanced_img = load_image(enhanced_cover_id)

                    # 创建拼接图片
                    if original_img and enhanced_img:
                        create_combined_image(original_img, enhanced_img, content_id, cover_source, output_dir)
                    else:
                        logger.warning(f"图片下载失败，跳过拼接: {content_id}")

                except Exception as e:
                    logger.error(f"图片处理失败 {content_id}: {e}")

            return output_row, None
        else:
            # 增强失败，不添加到输出表格
            return None, f"Enhancement failed for {content_id}: {result}"

    except Exception as e:
        error_msg = f"Error processing {content_id}: {str(e)}"
        logger.error(error_msg)
        return None, error_msg


def run_test_with_config(test_config, input_file="tests/3_top2k_with_results.csv"):
    """
    使用指定配置运行测试
    
    Args:
        test_config: 测试配置字典，包含enhance_classes, version, version2lutname等
        input_file: 输入CSV文件路径
    """
    # 生成输出文件名，包含类别和版本信息
    classes_str = "_".join(test_config['enhance_classes'])
    version = test_config['version']
    lut_name = test_config['version2lutname'].get(version, 'unknown')
    
    output_file = f"tests/top2k_enhanced_{classes_str}_v{version}_{lut_name}.csv"
    
    # 创建图片输出目录，包含类别和版本信息
    image_output_dir = None
    if DOWNLOAD_IMAGES:
        image_dir_name = f"enhanced_images_{classes_str}_v{version}_{lut_name}"
        image_output_dir = Path("tests") / image_dir_name
        image_output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"图片输出目录: {image_output_dir}")

    logger.info(f"开始测试配置: {test_config}")
    logger.info(f"输入文件: {input_file}")
    logger.info(f"输出文件: {output_file}")

    try:
        df = pd.read_csv(input_file)
        logger.info(f"加载了 {len(df)} 行数据")
    except Exception as e:
        logger.error(f"读取CSV文件失败: {e}")
        return

    # 准备结果列表
    results = []
    errors = []
    skipped = []

    # 并发处理配置
    max_workers = 8  # 可以根据需要调整并发数

    logger.info(f"开始处理，使用 {max_workers} 个工作线程...")

    # 使用ThreadPoolExecutor进行并发处理
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_row = {
            executor.submit(process_single_row, row.to_dict(), test_config, image_output_dir): idx
            for idx, row in df.iterrows()
        }
        
        # 使用tqdm显示进度条
        with tqdm(total=len(future_to_row), desc=f"Processing {classes_str}_v{version}") as pbar:
            for future in as_completed(future_to_row):
                row_idx = future_to_row[future]
                try:
                    result, error = future.result()
                    if result:
                        results.append(result)
                    if error:
                        if "skipping" in error:
                            skipped.append(f"Row {row_idx}: {error}")
                        elif "Enhancement failed" in error:
                            errors.append(f"Row {row_idx}: {error}")
                        else:
                            errors.append(f"Row {row_idx}: {error}")
                except Exception as e:
                    error_msg = f"Row {row_idx}: Exception in future: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                
                pbar.update(1)
    
    # 保存结果
    if results:
        result_df = pd.DataFrame(results)
        result_df.to_csv(output_file, index=False)
        logger.info(f"保存了 {len(results)} 个结果到 {output_file}")
    else:
        logger.warning("没有结果需要保存")
    
    # 报告错误和跳过的记录
    if errors:
        error_file = f"tests/enhancement_errors_{classes_str}_v{version}_{lut_name}.log"
        with open(error_file, 'w', encoding='utf-8') as f:
            for error in errors:
                f.write(f"{error}\n")
        logger.warning(f"遇到 {len(errors)} 个错误，保存到 {error_file}")

    if skipped:
        skip_file = f"tests/enhancement_skipped_{classes_str}_v{version}_{lut_name}.log"
        with open(skip_file, 'w', encoding='utf-8') as f:
            for skip in skipped:
                f.write(f"{skip}\n")
        logger.info(f"跳过了 {len(skipped)} 行（空封面字段），保存到 {skip_file}")

    logger.info("处理完成!")
    print(f"\n配置 {classes_str}_v{version}_{lut_name} 的测试总结:")
    print(f"- 总处理行数: {len(df)}")
    print(f"- 成功结果: {len(results)}")
    print(f"- 跳过行数（空封面）: {len(skipped)}")
    print(f"- 错误数: {len(errors)}")
    print(f"- 输出文件: {output_file}")
    if DOWNLOAD_IMAGES and image_output_dir:
        print(f"- 图片输出目录: {image_output_dir}")
    if errors:
        print(f"- 错误日志: tests/enhancement_errors_{classes_str}_v{version}_{lut_name}.log")
    if skipped:
        print(f"- 跳过日志: tests/enhancement_skipped_{classes_str}_v{version}_{lut_name}.log")
    
    return {
        'config': test_config,
        'total_rows': len(df),
        'successful': len(results),
        'skipped': len(skipped),
        'errors': len(errors),
        'output_file': output_file,
        'image_dir': str(image_output_dir) if image_output_dir else None
    }


def run_single_test(enhance_classes, version, version2lutname, input_file="tests/3_top2k_with_results.csv"):
    """
    运行单个测试配置的便捷函数

    Args:
        enhance_classes: 增强类别列表，如 ["person"] 或 ["food", "landscape"]
        version: 版本号
        version2lutname: 版本到LUT名称的映射字典
        input_file: 输入CSV文件路径
    """
    test_config = {
        'enhance_classes': enhance_classes,
        'version': version,
        'version2lutname': version2lutname
    }

    return run_test_with_config(test_config, input_file)


if __name__ == "__main__":
    version2lutname = {8: "hanshi09",
                        9: "hanshi21",
                        10: "hanshi20",
                        11: "riza",
                        12: "bingyuhuo",
                        13: "bingyuhuo"
                        }

    # 定义测试配置列表
    test_configs = [
        {
            'enhance_classes': ["person"],
            'version': 8,
            'version2lutname': version2lutname
        },
        {
            'enhance_classes': ["landscape"],
            'version': 12,
            'version2lutname': version2lutname
        },
        {
            'enhance_classes': ["food"],
            'version': 13,
            'version2lutname': version2lutname
        },
        {
            'enhance_classes': ["person"],
            'version': 9,
            'version2lutname': version2lutname
        },
        {
            'enhance_classes': ["person"],
            'version': 10,
            'version2lutname': version2lutname
        },
        {
            'enhance_classes': ["person"],
            'version': 11,
            'version2lutname': version2lutname
        },
    ]

    input_file = "tests/3_top2k_with_results.csv"

    logger.info(f"开始运行 {len(test_configs)} 个测试配置")

    all_results = []

    for i, config in enumerate(test_configs, 1):
        logger.info(f"\n{'='*50}")
        logger.info(f"运行测试配置 {i}/{len(test_configs)}")
        logger.info(f"{'='*50}")

        result = run_test_with_config(config, input_file)
        all_results.append(result)

        # 在配置之间添加短暂延迟
        if i < len(test_configs):
            time.sleep(2)

    # 打印所有测试的总结
    print(f"\n{'='*60}")
    print("所有测试配置完成总结:")
    print(f"{'='*60}")

    for i, result in enumerate(all_results, 1):
        config = result['config']
        classes_str = "_".join(config['enhance_classes'])
        version = config['version']
        lut_name = config['version2lutname'].get(version, 'unknown')

        print(f"\n{i}. 配置: {classes_str}_v{version}_{lut_name}")
        print(f"   - 成功: {result['successful']}/{result['total_rows']}")
        print(f"   - 输出文件: {result['output_file']}")
        if result['image_dir']:
            print(f"   - 图片目录: {result['image_dir']}")
